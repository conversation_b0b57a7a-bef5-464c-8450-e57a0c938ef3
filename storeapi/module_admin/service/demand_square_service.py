from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, update, text
from sqlalchemy.orm import selectinload
from typing import Optional, Tuple, List
from datetime import datetime, timedelta
from decimal import Decimal
import math
import uuid
import time
import random

from module_admin.entity.do.demand_square import DemandSquare
from module_admin.entity.do.service_staff import ServiceStaff
from module_admin.entity.do.order import Order
from module_admin.entity.do.store import Store
from module_admin.entity.do.service_product import ServiceProduct
from module_admin.entity.vo.demand_square_vo import (
    DemandSquareQueryVO,
    DemandSquareResponseVO,
    DemandSquareListResponseVO,
    DemandSquareStatsVO,
    DemandGrabRequestVO,
    DemandGrabResponseVO,
    ShareOrderRequestVO,
    StaffSelectionRequestVO,
    AvailableStaffVO
)
from exceptions.exception import ValidationException, BusinessException, QueryException, DatabaseException
from utils.log_util import logger

class DemandSquareService:
    """需求广场服务类"""
    
    @staticmethod
    async def get_demand_list(
        db: AsyncSession, 
        query_params: DemandSquareQueryVO
    ) -> DemandSquareListResponseVO:
        """
        获取需求列表
        """
        try:
            # 构建查询条件
            current_time = datetime.now()
            conditions = [
                DemandSquare.is_delete == 0,  # 未删除
            ]
            
            # 业务类型筛选
            if query_params.business_type is not None:
                conditions.append(DemandSquare.business_type == query_params.business_type)
            
            # 需求状态筛选
            if query_params.demand_status is not None:
                conditions.append(DemandSquare.demand_status == query_params.demand_status)
            
            # 查询总数
            count_query = select(func.count(DemandSquare.id)).where(and_(*conditions))
            total_result = await db.execute(count_query)
            total = total_result.scalar() or 0
            
            # 计算分页
            offset = (query_params.page - 1) * query_params.size
            pages = math.ceil(total / query_params.size) if total > 0 else 0
            
            # 查询数据
            data_query = (
                select(DemandSquare)
                .where(and_(*conditions))
                .order_by(DemandSquare.create_time.desc())
                .offset(offset)
                .limit(query_params.size)
            )
            
            result = await db.execute(data_query)
            demands = result.scalars().all()
            
            # 转换为响应VO
            demand_list = []
            
            for demand in demands:
                # 根据订单来源和业务类型计算剩余时间（秒）
                remaining_seconds = None

                # 计算过期时间 - 区分订单来源
                if demand.source_order_id:
                    # 门店共享的订单：使用用户设置的自定义过期时间
                    calculated_expire_time = demand.expire_time
                else:
                    # 云平台同步的订单：使用固定过期时间
                    if demand.business_type == 1:  # 到家订单：30分钟
                        calculated_expire_time = demand.create_time + timedelta(minutes=30)
                    elif demand.business_type == 2:  # 到店订单：24小时
                        calculated_expire_time = demand.create_time + timedelta(hours=24)
                    else:
                        # 默认使用数据库中的expire_time
                        calculated_expire_time = demand.expire_time

                # 计算剩余时间和动态状态
                is_expired = calculated_expire_time <= current_time
                if not is_expired:
                    remaining_seconds = int((calculated_expire_time - current_time).total_seconds())

                # 动态设置状态：如果已过期且原状态是待抢单，则设为已过期
                dynamic_status = demand.demand_status
                if is_expired and demand.demand_status == 1:
                    dynamic_status = -1  # 已过期

                # 状态文本
                status_map = {
                    -1: "已过期",
                    1: "待抢单",
                    2: "已抢单"
                }
                status_text = status_map.get(dynamic_status, "未知")
                
                # 业务类型文本
                business_type_map = {
                    1: "到家服务",
                    2: "到店服务"
                }
                business_type_text = business_type_map.get(demand.business_type, "未知")
                
                demand_vo = DemandSquareResponseVO(
                    id=demand.id,
                    uuid=demand.uuid,
                    platform_order_number=demand.platform_order_number,
                    business_type=demand.business_type,
                    commission_amount=demand.commission_amount,
                    service_project=demand.service_project,
                    service_address=demand.service_address,
                    service_time=demand.service_time,
                    demand_status=dynamic_status,  # 使用动态状态
                    customer_name=demand.customer_name,
                    customer_phone=demand.customer_phone,
                    service_requirements=demand.service_requirements,
                    source_platform=demand.source_platform,
                    expire_time=calculated_expire_time,  # 使用计算的过期时间
                    create_time=demand.create_time,
                    remaining_seconds=remaining_seconds,
                    status_text=status_text,
                    business_type_text=business_type_text
                )
                demand_list.append(demand_vo)
            
            return DemandSquareListResponseVO(
                total=total,
                page=query_params.page,
                size=query_params.size,
                pages=pages,
                list=demand_list
            )
            
        except Exception as e:
            logger.error(f"获取需求列表失败: {str(e)}")
            raise e
    
    @staticmethod
    async def get_demand_stats(
        db: AsyncSession,
        business_type: Optional[int] = None
    ) -> DemandSquareStatsVO:
        """
        获取需求统计信息
        """
        try:
            # 基础查询条件
            current_time = datetime.now()
            base_conditions = [
                DemandSquare.is_delete == 0,
                DemandSquare.demand_status == 1,  # 待抢单
                # 根据业务类型判断是否过期
                or_(
                    # 到家订单：创建时间+30分钟内有效
                    and_(
                        DemandSquare.business_type == 1,
                        DemandSquare.create_time + timedelta(minutes=30) > current_time
                    ),
                    # 到店订单：创建时间+24小时内有效
                    and_(
                        DemandSquare.business_type == 2,
                        DemandSquare.create_time + timedelta(hours=24) > current_time
                    ),
                    # 其他类型使用原有expire_time逻辑
                    and_(
                        DemandSquare.business_type.notin_([1, 2]),
                        DemandSquare.expire_time > current_time
                    )
                )
            ]
            
            # 总统计
            conditions = base_conditions.copy()
            if business_type is not None:
                conditions.append(DemandSquare.business_type == business_type)
            
            # 查询总数和总金额
            stats_query = select(
                func.count(DemandSquare.id).label('total_count'),
                func.coalesce(func.sum(DemandSquare.commission_amount), 0).label('total_amount')
            ).where(and_(*conditions))
            
            stats_result = await db.execute(stats_query)
            stats = stats_result.first()
            
            # 分类统计
            home_query = select(func.count(DemandSquare.id)).where(
                and_(
                    *base_conditions,
                    DemandSquare.business_type == 1
                )
            )
            home_result = await db.execute(home_query)
            home_count = home_result.scalar() or 0
            
            store_query = select(func.count(DemandSquare.id)).where(
                and_(
                    *base_conditions,
                    DemandSquare.business_type == 2
                )
            )
            store_result = await db.execute(store_query)
            store_count = store_result.scalar() or 0
            
            return DemandSquareStatsVO(
                total_count=stats.total_count or 0,
                total_amount=Decimal(str(stats.total_amount or 0)),
                home_service_count=home_count,
                store_service_count=store_count
            )
            
        except Exception as e:
            logger.error(f"获取需求统计失败: {str(e)}")
            raise e

    @staticmethod
    async def grab_demand(
        db: AsyncSession,
        grab_data: DemandGrabRequestVO,
        current_user
    ) -> DemandGrabResponseVO:
        """
        抢单处理逻辑
        """
        try:
            # 1. 参数验证
            if not grab_data.demand_uuid:
                raise ValidationException(message="需求UUID不能为空")

            if not grab_data.store_uuid:
                raise ValidationException(message="门店UUID不能为空")

            # 2. 获取当前用户信息
            user_id = getattr(current_user.user, 'id', None)
            user_uuid = getattr(current_user.user, 'uuid', None)
            user_name = getattr(current_user.user, 'name', None) or getattr(current_user.user, 'nick_name', '未知用户')

            if not user_id or not user_uuid:
                raise ValidationException(message="用户信息获取失败")

            logger.info(f"用户 {user_name}({user_id}) 尝试抢单: {grab_data.demand_uuid}")

            # 3. 验证需求和门店信息（优化：合并查询）
            validation_result = await DemandSquareService._validate_grab_prerequisites(
                db, grab_data.demand_uuid, grab_data.store_uuid
            )

            if not validation_result["success"]:
                return DemandGrabResponseVO(
                    success=False,
                    message=validation_result["message"],
                    demand_uuid=grab_data.demand_uuid
                )

            demand = validation_result["demand"]
            store_info = validation_result["store_info"]

            # 7. 开始事务处理抢单
            try:
                # 在事务开始前获取所有需要的demand属性值（优化：避免事务中访问ORM属性）
                demand_business_type = demand.business_type
                demand_uuid_str = grab_data.demand_uuid
                demand_commission_amount = float(demand.commission_amount)
                demand_service_address = demand.service_address
                demand_service_time = demand.service_time
                demand_customer_name = demand.customer_name
                demand_customer_phone = demand.customer_phone
                demand_platform_order_number = demand.platform_order_number
                demand_remark = demand.remark
                demand_lng = demand.lng
                demand_lat = demand.lat
                demand_customer_address = demand.customer_address
                demand_service_requirements = demand.service_requirements

                # 7.1 原子性更新需求状态（使用乐观锁）
                grab_time = datetime.now()
                update_demand_query = update(DemandSquare).where(
                    and_(
                        DemandSquare.uuid == demand_uuid_str,
                        DemandSquare.demand_status == 1  # 确保状态仍为待抢单
                    )
                ).values(
                    demand_status=2,
                    grab_store_uuid=grab_data.store_uuid,
                    grab_store_name=store_info._mapping.get("name"),
                    grab_user_uuid=user_uuid,  # 使用用户的UUID而不是ID
                    grab_user_name=user_name,
                    grab_time=grab_time,
                    update_time=grab_time
                )

                update_result = await db.execute(update_demand_query)

                # 检查是否更新成功（并发控制）
                if update_result.rowcount == 0:
                    return DemandGrabResponseVO(
                        success=False,
                        message="抢单失败，该需求已被其他门店抢走",
                        demand_uuid=demand_uuid_str
                    )

                # 7.2 根据业务类型和是否为共享订单创建不同的记录
                if demand_business_type == 1:  # 到家订单
                    # 检查是否为共享订单
                    if demand.source_order_id:
                        # 共享订单：不创建新订单，只更新需求状态
                        logger.info(f"共享订单抢单成功，不创建新订单: source_order_id={demand.source_order_id}")
                        created_record_id = f"shared_order_{demand.source_order_id}"
                    else:
                        # 普通订单：创建新订单
                        # 生成订单号
                        timestamp = int(time.time())
                        random_num = random.randint(100000, 999999)
                        order_number = f"DS{timestamp}{random_num}"

                        # 创建订单记录
                        await DemandSquareService._create_order_from_demand(
                            db, demand, store_info, order_number, user_id, {
                                "commission_amount": demand_commission_amount,
                                "service_address": demand_service_address,
                                "service_time": demand_service_time,
                                "customer_name": demand_customer_name,
                                "customer_phone": demand_customer_phone,
                                "platform_order_number": demand_platform_order_number,
                                "remark": demand_remark,
                                "lng": demand_lng,
                                "lat": demand_lat,
                                "customer_address": demand_customer_address,
                                "service_requirements": demand_service_requirements
                            }
                        )

                        # 更新需求记录的订单号
                        update_order_number_query = update(DemandSquare).where(
                            DemandSquare.uuid == demand_uuid_str
                        ).values(
                            created_order_number=order_number,
                            update_time=grab_time
                        )
                        await db.execute(update_order_number_query)

                        # 记录操作日志
                        await DemandSquareService._create_order_log(
                            db, order_number, user_id, user_name, "抢单成功"
                        )

                        created_record_id = order_number

                elif demand_business_type == 2:  # 到店订单
                    # 创建customer线索记录
                    customer_uuid = await DemandSquareService._create_customer_lead_from_demand(
                        db, demand, store_info, user_id, user_uuid, user_name
                    )

                    # 更新需求记录的线索UUID
                    update_customer_uuid_query = update(DemandSquare).where(
                        DemandSquare.uuid == demand_uuid_str
                    ).values(
                        created_order_number=customer_uuid,  # 复用这个字段存储线索UUID
                        update_time=grab_time
                    )
                    await db.execute(update_customer_uuid_query)

                    created_record_id = customer_uuid

                else:
                    raise BusinessException(message=f"不支持的业务类型: {demand_business_type}")

                # 🔥 在事务提交前获取所有需要的数据，避免异步错误
                is_shared_order = demand.source_order_id is not None

                # 提交事务
                await db.commit()

                logger.info(f"抢单成功: 用户{user_name}抢到需求{demand_uuid_str}，创建记录{created_record_id}")

                # 🔥 使用预获取的数据，避免访问已失效的ORM对象
                if demand_business_type == 1:
                    if is_shared_order:
                        success_message = "抢单成功，请选择服务人员"
                    else:
                        success_message = "抢单成功，订单已创建"
                else:
                    success_message = "抢单成功，线索已创建"

                return DemandGrabResponseVO(
                    success=True,
                    message=success_message,
                    order_number=created_record_id,
                    demand_uuid=demand_uuid_str,
                    grab_time=grab_time,
                    need_staff_selection=is_shared_order and demand_business_type == 1
                )

            except Exception as e:
                # 回滚事务
                await db.rollback()
                logger.error(f"抢单事务处理失败: {str(e)}")
                raise e

        except ValidationException as e:
            logger.error(f"抢单参数验证失败: {e.message}")
            raise e
        except BusinessException as e:
            logger.error(f"抢单业务异常: {e.message}")
            raise e
        except Exception as e:
            logger.error(f"抢单服务异常: {str(e)}")
            raise BusinessException(message=f"抢单失败: {str(e)}")

    @staticmethod
    async def _validate_grab_prerequisites(
        db: AsyncSession,
        demand_uuid: str,
        store_uuid: str
    ) -> dict:
        """
        验证抢单前置条件（优化：合并多个验证查询）
        """
        try:
            # 合并查询需求和门店信息，同时获取共享订单的原始门店信息
            validation_query = text("""
                SELECT
                    'demand' as type, d.uuid, d.demand_status, d.business_type,
                    d.create_time, d.expire_time, d.source_order_id,
                    NULL as name, NULL as store_uuid, NULL as id, NULL as original_store_uuid
                FROM demand_square d
                WHERE d.uuid = :demand_uuid AND d.is_delete = 0
                UNION ALL
                SELECT
                    'store' as type, NULL as uuid, NULL as demand_status, NULL as business_type,
                    NULL as create_time, NULL as expire_time, NULL as source_order_id,
                    s.name, s.store_uuid, s.id, NULL as original_store_uuid
                FROM store s
                WHERE s.store_uuid = :store_uuid AND s.is_delete = 0 AND s.status = 1
                UNION ALL
                SELECT
                    'original_order' as type, NULL as uuid, NULL as demand_status, NULL as business_type,
                    NULL as create_time, NULL as expire_time, NULL as source_order_id,
                    NULL as name, NULL as store_uuid, NULL as id, o.store_uuid as original_store_uuid
                FROM demand_square d
                LEFT JOIN `order` o ON d.source_order_id = o.id
                WHERE d.uuid = :demand_uuid AND d.source_order_id IS NOT NULL
            """)

            validation_result = await db.execute(validation_query, {
                "demand_uuid": demand_uuid,
                "store_uuid": store_uuid
            })
            validation_data = validation_result.fetchall()

            demand_info = None
            store_info = None
            original_store_uuid = None

            for row in validation_data:
                if row.type == 'demand':
                    demand_info = row
                elif row.type == 'store':
                    store_info = row
                elif row.type == 'original_order' and row.original_store_uuid:
                    original_store_uuid = row.original_store_uuid

            # 验证需求是否存在
            if not demand_info:
                return {
                    "success": False,
                    "message": "需求不存在或已删除"
                }

            # 验证需求状态
            if demand_info.demand_status != 1:
                status_text = "已抢单" if demand_info.demand_status == 2 else "已过期"
                return {
                    "success": False,
                    "message": f"需求状态不允许抢单，当前状态: {status_text}"
                }

            # 🔥 新增：验证是否为自抢单（共享订单）
            if demand_info.source_order_id and original_store_uuid:
                logger.info(f"检查自抢单: 需求UUID={demand_uuid}, 原始门店={original_store_uuid}, 抢单门店={store_uuid}")
                if original_store_uuid == store_uuid:
                    logger.warning(f"阻止自抢单: 门店{store_uuid}尝试抢取自己发布的订单，需求UUID={demand_uuid}")
                    return {
                        "success": False,
                        "message": "不能抢取自己门店发布的订单"
                    }
                else:
                    logger.info(f"允许抢单: 门店{store_uuid}抢取其他门店{original_store_uuid}发布的订单")

            # 验证是否过期 - 根据订单来源使用不同的过期时间逻辑
            current_time = datetime.now()

            if demand_info.source_order_id:
                # 门店共享的订单：使用用户设置的自定义过期时间
                expire_time = demand_info.expire_time
                logger.info(f"门店共享订单过期时间检查: demand_uuid={demand_uuid}, expire_time={expire_time}")
            else:
                # 云平台同步的到家订单：使用固定30分钟过期时间
                if demand_info.business_type == 1:  # 到家订单
                    expire_time = demand_info.create_time + timedelta(minutes=30)
                    logger.info(f"云平台到家订单过期时间检查: demand_uuid={demand_uuid}, create_time={demand_info.create_time}, expire_time={expire_time}")
                elif demand_info.business_type == 2:  # 到店订单
                    expire_time = demand_info.create_time + timedelta(hours=24)
                    logger.info(f"云平台到店订单过期时间检查: demand_uuid={demand_uuid}, create_time={demand_info.create_time}, expire_time={expire_time}")
                else:
                    # 其他类型使用数据库存储的过期时间
                    expire_time = demand_info.expire_time
                    logger.info(f"其他类型订单过期时间检查: demand_uuid={demand_uuid}, expire_time={expire_time}")

            if current_time > expire_time:
                return {
                    "success": False,
                    "message": "需求已过期，无法抢单"
                }

            # 验证门店是否存在
            if not store_info:
                return {
                    "success": False,
                    "message": "门店不存在或已停用"
                }

            # 获取完整的需求对象
            demand_query = select(DemandSquare).where(DemandSquare.uuid == demand_uuid)
            demand_result = await db.execute(demand_query)
            demand = demand_result.scalar_one()

            return {
                "success": True,
                "demand": demand,
                "store_info": store_info
            }

        except Exception as e:
            logger.error(f"验证抢单前置条件失败: {str(e)}")
            return {
                "success": False,
                "message": f"验证失败: {str(e)}"
            }

    @staticmethod
    async def _create_order_from_demand(
        db: AsyncSession,
        demand: DemandSquare,
        store_info,
        order_number: str,
        user_id: int,
        demand_data: Optional[dict] = None
    ):
        """
        从需求创建订单记录（优化：使用预获取的数据避免ORM属性访问）
        """
        try:
            # 使用传入的数据或从demand对象获取（向后兼容）
            if demand_data:
                commission_amount = demand_data["commission_amount"]
                service_address = demand_data["service_address"]
                service_time = demand_data["service_time"]
                customer_name = demand_data["customer_name"]
                customer_phone = demand_data["customer_phone"]
                platform_order_number = demand_data["platform_order_number"]
                remark = demand_data["remark"]
            else:
                commission_amount = float(demand.commission_amount)
                service_address = demand.service_address
                service_time = demand.service_time
                customer_name = demand.customer_name
                customer_phone = demand.customer_phone
                platform_order_number = demand.platform_order_number
                remark = demand.remark

            # 1. 批量查询产品、SKU和客户信息（优化：减少数据库查询次数）
            batch_query = text("""
                SELECT
                    'product' as type, p.id, p.product_name as name, NULL as mobile, NULL as uuid, NULL as sku_id
                FROM product p
                WHERE p.id = :product_id AND p.is_delete = 0
                UNION ALL
                SELECT
                    'product_sku' as type, ps.productid as id, ps.name, NULL as mobile, NULL as uuid, ps.id as sku_id
                FROM product_sku ps
                WHERE ps.productid = :product_id
                UNION ALL
                SELECT
                    'customer' as type, c.id, c.name, c.mobile, c.uuid, NULL as sku_id
                FROM ccuser c
                WHERE c.name = :customer_name AND c.mobile = :customer_phone AND c.is_delete != '1'
                LIMIT 3
            """)
            batch_result = await db.execute(batch_query, {
                "product_id": demand.jingang_product_id,
                "customer_name": customer_name,
                "customer_phone": customer_phone
            })
            batch_data = batch_result.fetchall()

            # 解析查询结果
            product_info = None
            product_sku_info = None
            customer_info = None
            for row in batch_data:
                if row.type == 'product':
                    product_info = row
                elif row.type == 'product_sku':
                    product_sku_info = row
                elif row.type == 'customer':
                    customer_info = row

            # 使用产品名称，如果没有找到产品则使用需求的服务项目
            product_name = product_info.name if product_info else demand.service_project
            product_id = product_info.id if product_info else None
            product_sku_id = product_sku_info.sku_id if product_sku_info else None

            # 2. 处理客户信息
            if customer_info:
                customer_id = customer_info.id
            else:
                # 创建新客户
                customer_id, _ = await DemandSquareService._create_customer_from_demand(
                    db, demand, store_info, user_id
                )

            # 3. 创建客户地址记录（参考代客下单逻辑）
            address_id = None
            if demand_data and demand_data.get("lng") and demand_data.get("lat"):
                address_id = await DemandSquareService._create_customer_address_from_demand(
                    db, customer_id, demand_data
                )

            # 创建订单SQL
            create_order_sql = text("""
                INSERT INTO `order` (
                    order_number, user_id, store_id, store_uuid, store_name,
                    product_id, product_name, product_type, product_type_name, product_sku_id,
                    service_address, service_date, service_type, service_hour,
                    order_status, order_status_name, buy_num, pay_actual,
                    total_pay_actual, source, create_time,
                    remark, service_phone, address_id
                ) VALUES (
                    :order_number, :user_id, :store_id, :store_uuid, :store_name,
                    :product_id, :product_name, :product_type, :product_type_name, :product_sku_id,
                    :service_address, :service_date, 1, :service_hour,
                    10, '已接单', 1, :pay_actual,
                    :total_pay_actual, 'grab', NOW(),
                    :remark, :service_phone, :address_id
                )
            """)

            # 4. 计算service_hour（从服务时间提取小时部分）
            service_hour = "08:00:00"  # 默认值
            if service_time is not None:
                try:
                    if isinstance(service_time, str):
                        from datetime import datetime
                        service_datetime = datetime.fromisoformat(service_time.replace('Z', '+00:00'))
                    else:
                        service_datetime = service_time
                    service_hour = service_datetime.strftime("%H:%M:%S")
                except Exception as e:
                    logger.warning(f"解析服务时间失败，使用默认值: {str(e)}")

            # 5. 执行插入，使用预获取的字段值
            await db.execute(create_order_sql, {
                "order_number": order_number,
                "user_id": customer_id,
                "store_id": store_info._mapping.get("id"),
                "store_uuid": store_info._mapping.get("store_uuid"),
                "store_name": store_info._mapping.get("name"),
                "product_id": product_id,
                "product_name": product_name,
                "product_type": 1,  # 固定为1
                "product_type_name": "服务",  # 固定为"服务"
                "product_sku_id": product_sku_id,
                "service_address": service_address,
                "service_date": service_time,
                "service_hour": service_hour,
                "pay_actual": commission_amount,
                "total_pay_actual": commission_amount,
                "remark": remark or f"抢单生成订单，平台订单号：{platform_order_number}",
                "service_phone": customer_phone,
                "address_id": address_id
            })

            # 4. 创建支付记录
            await DemandSquareService._create_payment_record_from_demand(
                db, order_number, commission_amount
            )

            logger.info(f"订单创建成功: {order_number}")

        except Exception as e:
            logger.error(f"创建订单失败: {str(e)}")
            raise BusinessException(message=f"创建订单失败: {str(e)}")

    @staticmethod
    async def _create_order_log(
        db: AsyncSession,
        order_number: str,
        operator_id: int,
        operator_name: str,
        operation_desc: str
    ):
        """
        创建订单操作日志
        """
        try:
            log_sql = text("""
                INSERT INTO order_logs (
                    order_number, operation_type, operation_desc,
                    operator_id, operator_name, operation_time, create_time
                ) VALUES (
                    :order_number, 1, :operation_desc,
                    :operator_id, :operator_name, NOW(), NOW()
                )
            """)

            await db.execute(log_sql, {
                "order_number": order_number,
                "operation_desc": operation_desc,
                "operator_id": operator_id,
                "operator_name": operator_name
            })

            logger.info(f"订单日志记录成功: {order_number} - {operation_desc}")

        except Exception as e:
            logger.error(f"记录订单日志失败: {str(e)}")
            # 日志记录失败不影响主流程，只记录错误

    @staticmethod
    async def _create_customer_from_demand(
        db: AsyncSession,
        demand: DemandSquare,
        store_info,
        user_id: int
    ) -> tuple[int, str]:
        """
        从需求创建客户记录（参考代客下单逻辑）
        """
        try:
            import uuid

            # 生成客户UUID
            customer_uuid = str(uuid.uuid4())

            # 插入新客户记录（参考代客下单的方式）
            insert_customer_sql = text("""
                INSERT INTO ccuser (
                    uuid, name, mobile, store_uuid, created_by, updated_by,
                    created_at, updated_at, is_delete, status, source
                ) VALUES (
                    :uuid, :name, :mobile, :store_uuid, 'grab_order', 'grab_order',
                    NOW(), NOW(), '0', '1', 'grab'
                )
            """)

            await db.execute(insert_customer_sql, {
                "uuid": customer_uuid,
                "name": demand.customer_name,
                "mobile": demand.customer_phone,
                "store_uuid": store_info._mapping.get("store_uuid")
            })

            # 查询新创建的客户信息（参考代客下单的方式）
            customer_query = text("""
                SELECT id, uuid, name, mobile, store_uuid
                FROM ccuser
                WHERE uuid = :uuid
            """)
            customer_result = await db.execute(customer_query, {"uuid": customer_uuid})
            customer_row = customer_result.fetchone()

            if not customer_row:
                raise BusinessException(message="创建客户后查询失败")

            customer_id = customer_row._mapping.get("id")
            if not customer_id:
                raise BusinessException(message="获取客户ID失败")

            logger.info(f"客户创建成功: {customer_uuid}, ID: {customer_id}")
            return int(customer_id), customer_uuid

        except Exception as e:
            logger.error(f"创建客户失败: {str(e)}")
            raise BusinessException(message=f"创建客户失败: {str(e)}")

    @staticmethod
    async def _create_customer_address_from_demand(
        db: AsyncSession,
        customer_id: int,
        demand_data: dict
    ) -> int:
        """
        从需求创建客户地址记录（参考代客下单逻辑）
        """
        try:
            # 插入地址记录到ccuser_extend表
            insert_address_sql = text("""
                INSERT INTO ccuser_extend (
                    ccuser_id, name, address, address_desc, lng, lat,
                    contact_phone, isDefault, created_by, updated_by,
                    created_at, updated_at
                ) VALUES (
                    :ccuser_id, :name, :address, :address_desc, :lng, :lat,
                    :contact_phone, 1, 'grab_order', 'grab_order',
                    NOW(), NOW()
                )
            """)

            await db.execute(insert_address_sql, {
                "ccuser_id": str(customer_id),
                "name": demand_data.get("customer_name", ""),
                "address": demand_data.get("service_address", ""),
                "address_desc": demand_data.get("service_requirements", ""),
                "lng": demand_data.get("lng", ""),
                "lat": demand_data.get("lat", ""),
                "contact_phone": demand_data.get("customer_phone", "")
            })

            # 获取新插入的地址ID
            address_id_query = text("""
                SELECT id FROM ccuser_extend
                WHERE ccuser_id = :ccuser_id AND contact_phone = :contact_phone
                ORDER BY id DESC LIMIT 1
            """)
            address_id_result = await db.execute(address_id_query, {
                "ccuser_id": str(customer_id),
                "contact_phone": demand_data.get("customer_phone", "")
            })
            address_id_row = address_id_result.fetchone()
            address_id = address_id_row._mapping.get("id") if address_id_row else None

            if not address_id:
                raise BusinessException(message="获取地址ID失败")

            logger.info(f"客户地址创建成功: customer_id={customer_id}, address_id={address_id}")
            return int(address_id)

        except Exception as e:
            logger.error(f"创建客户地址失败: {str(e)}")
            raise BusinessException(message=f"创建客户地址失败: {str(e)}")

    @staticmethod
    async def _create_payment_record_from_demand(
        db: AsyncSession,
        order_number: str,
        commission_amount: float
    ):
        """
        创建支付记录
        """
        try:
            current_time = datetime.now()

            # 创建支付记录
            create_payment_sql = text("""
                INSERT INTO order_payments (
                    order_number, pay_money, pay_actual, pay_type, pay_type_name,
                    pay_status, create_time, update_time
                ) VALUES (
                    :order_number, :pay_money, :pay_actual, :pay_type, :pay_type_name,
                    1, :create_time, :update_time
                )
            """)

            # 执行插入
            await db.execute(create_payment_sql, {
                "order_number": order_number,
                "pay_money": commission_amount,
                "pay_actual": commission_amount,
                "pay_type": "106",  # 现金支付
                "pay_type_name": "现金支付",
                "create_time": current_time,
                "update_time": current_time
            })

            logger.info(f"支付记录创建成功: {order_number}")

        except Exception as e:
            logger.error(f"创建支付记录失败: {str(e)}")
            raise BusinessException(message=f"创建支付记录失败: {str(e)}")

    @staticmethod
    async def _create_customer_lead_from_demand(
        db: AsyncSession,
        demand: DemandSquare,
        store_info,
        user_id: int,
        user_uuid: str,
        user_name: str
    ) -> str:
        """
        从需求创建customer线索记录（到店订单）
        """
        try:
            import uuid

            # 生成线索UUID
            customer_uuid = str(uuid.uuid4()).replace('-', '')
            current_time = datetime.now()

            # 解析服务地址获取城市信息
            service_address = demand.service_address or ""
            city_name = "未知城市"

            # 简单的城市提取逻辑（可以根据实际需求优化）
            for city in ["厦门", "福州", "泉州", "漳州", "龙岩", "三明", "莆田", "南平", "宁德"]:
                if city in service_address:
                    city_name = city
                    break

            # 查询城市ID
            city_query = text("""
                SELECT id FROM city_information
                WHERE name = :city_name OR name = :city_name_with_suffix
                LIMIT 1
            """)
            city_result = await db.execute(city_query, {
                "city_name": city_name,
                "city_name_with_suffix": f"{city_name}市"
            })
            city_info = city_result.fetchone()
            city_id = city_info.id if city_info else None

            # 创建customer线索记录
            create_customer_sql = text("""
                INSERT INTO customer (
                    uuid, name, mobile, address, city, city_id,
                    aunt_type, aunt_name, source, status, status_name,
                    user_uuid, user_name, store_uuid, remark,
                    create_time, update_time, is_delete, common_status,
                    demand_id
                ) VALUES (
                    :uuid, :name, :mobile, :address, :city, :city_id,
                    :aunt_type, :aunt_name, 'grab', '1', '待跟进',
                    :user_uuid, :user_name, :store_uuid, :remark,
                    :create_time, :update_time, '0', '0',
                    :demand_id
                )
            """)

            # 执行插入
            await db.execute(create_customer_sql, {
                "uuid": customer_uuid,
                "name": demand.customer_name,
                "mobile": demand.customer_phone,
                "address": service_address,
                "city": city_name,
                "city_id": city_id,
                "aunt_type": demand.service_project,  # 服务项目作为阿姨类型
                "aunt_name": demand.service_project,
                "user_uuid": user_uuid,  # 使用用户的UUID
                "user_name": user_name,
                "store_uuid": store_info._mapping.get("store_uuid"),
                "remark": f"抢单生成线索，平台订单号：{demand.platform_order_number}",
                "create_time": current_time,
                "update_time": current_time,
                "demand_id": demand.uuid
            })

            logger.info(f"线索创建成功: {customer_uuid}")
            return customer_uuid

        except Exception as e:
            logger.error(f"创建线索失败: {str(e)}")
            raise BusinessException(message=f"创建线索失败: {str(e)}")

    @staticmethod
    async def share_order_to_square(
        db: AsyncSession,
        share_request,
        current_user
    ) -> dict:
        """
        共享订单到家政广场
        """
        try:
            from module_admin.entity.do.order import Order
            from module_admin.entity.do.store import Store
            from datetime import datetime, timedelta
            import uuid

            # 1. 验证订单是否存在且属于当前用户门店，同时获取客户信息
            from module_admin.entity.do.ccuser import CCUser
            from module_admin.entity.do.ccuser_extend import CCUserExtend

            order_query = select(
                Order.id,
                Order.order_number,
                Order.user_id,
                Order.store_id,
                Order.store_uuid,
                Order.product_id,
                Order.product_name,
                Order.service_type,
                Order.service_address,
                Order.service_date,
                Order.service_phone,
                Order.order_status,
                Order.pay_actual,
                Order.remark,
                Order.service_remark,  # 服务要求字段
                Order.address_id,
                # 从 ccuser_extend 获取客户信息和地理位置（通过 address_id 关联获取订单地址）
                CCUserExtend.name.label('customer_name_extend'),
                CCUserExtend.contact_phone.label('customer_phone_extend'),
                CCUserExtend.lng,
                CCUserExtend.lat,
                # 从 ccuser 获取备用客户信息（通过 user_id 关联）
                CCUser.name.label('customer_name_ccuser')
            ).outerjoin(
                CCUserExtend, Order.address_id == CCUserExtend.id  # 通过地址ID关联获取地址信息
            ).outerjoin(
                CCUser, Order.user_id == CCUser.id  # 通过用户ID关联获取用户信息
            ).where(Order.id == share_request.order_id)

            order_result = await db.execute(order_query)
            order = order_result.first()

            if not order:
                return {"success": False, "message": "订单不存在"}

            # 获取用户地址信息（通过 user_id 关联）
            user_address_query = select(
                CCUserExtend.address
            ).where(
                CCUserExtend.ccuser_id == order.user_id
            ).order_by(CCUserExtend.id.desc()).limit(1)  # 取最新的地址记录

            user_address_result = await db.execute(user_address_query)
            user_address_row = user_address_result.first()
            user_address = user_address_row.address if user_address_row else None

            # 2. 验证订单归属权（只能共享本门店的订单）
            user_store_uuid = current_user.user.store_uuid
            if order.store_uuid != user_store_uuid:
                return {"success": False, "message": "只能共享本门店的订单"}

            # 3. 验证订单状态（只有已支付的到家订单可以共享）
            if order.service_type != 1:
                return {"success": False, "message": "只有到家订单可以共享"}

            if order.order_status != 10:
                return {"success": False, "message": "只有已支付的订单可以共享"}

            # 4. 检查订单是否已经共享过
            existing_share_query = select(DemandSquare).where(
                DemandSquare.source_order_id == share_request.order_id
            )
            existing_share_result = await db.execute(existing_share_query)
            existing_share = existing_share_result.scalar_one_or_none()

            if existing_share:
                return {"success": False, "message": "该订单已经共享过了"}

            # 5. 获取门店信息
            store_query = select(Store).where(Store.id == order.store_id)
            store_result = await db.execute(store_query)
            store = store_result.scalar_one_or_none()

            if not store:
                return {"success": False, "message": "门店信息不存在"}

            # 6. 创建demand_square记录，正确映射所有字段
            demand_uuid = str(uuid.uuid4())
            expire_time = datetime.now() + timedelta(hours=share_request.expire_hours)

            # 确定客户姓名：优先使用 ccuser_extend.name，其次使用 ccuser.name
            customer_name = order.customer_name_extend or order.customer_name_ccuser

            # 确定客户电话：优先使用 ccuser_extend.contact_phone，其次使用 order.service_phone
            customer_phone = order.customer_phone_extend or order.service_phone

            # 确定客户地址：优先使用用户地址，其次使用订单服务地址
            customer_address = user_address or order.service_address

            demand_data = DemandSquare(
                uuid=demand_uuid,
                platform_order_number=demand_uuid,  # 使用需求UUID作为平台订单号（满足非空约束）
                jingang_order_number=order.order_number,  # 订单号填写到jingang_order_number字段
                jingang_product_id=str(order.product_id) if order.product_id else None,  # 产品ID映射
                source_order_id=order.id,  # 标记为共享订单
                business_type=1,  # 到家服务
                business_status=1,  # 实单
                commission_amount=share_request.commission_amount,  # 使用用户设置的佣金金额
                service_project=order.product_name,
                service_address=order.service_address,
                service_time=order.service_date,
                lng=order.lng,  # 地理位置坐标
                lat=order.lat,  # 地理位置坐标
                demand_status=1,  # 待抢单
                expire_time=expire_time,
                customer_name=customer_name,  # 通过关联查询获取的客户姓名
                customer_phone=customer_phone,  # 优先使用联系电话
                customer_address=customer_address,  # 客户地址
                service_requirements=order.service_remark,  # 服务要求字段
                remark=order.remark,  # 备注信息
                source_platform="order_share",  # 标记来源为订单共享
                create_time=datetime.now(),
                update_time=datetime.now()
            )

            db.add(demand_data)
            await db.commit()

            logger.info(f"订单共享成功: order_id={share_request.order_id}, demand_uuid={demand_uuid}")
            return {"success": True, "message": "订单共享成功", "demand_uuid": demand_uuid}

        except Exception as e:
            await db.rollback()
            logger.error(f"订单共享失败: {str(e)}")
            return {"success": False, "message": f"订单共享失败: {str(e)}"}

    @staticmethod
    async def get_available_staff_list(
        db: AsyncSession,
        store_uuid: str,
        current_user
    ) -> dict:
        """
        获取门店可用员工列表
        """
        try:
            from module_admin.entity.do.service_staff import ServiceStaff
            from module_admin.entity.vo.demand_square_vo import AvailableStaffVO

            # 查询门店的可用员工
            staff_query = select(ServiceStaff).where(
                and_(
                    ServiceStaff.store_uuid == store_uuid,
                    ServiceStaff.status == 1,  # 启用状态
                    ServiceStaff.is_delete == 0  # 未删除
                )
            ).order_by(ServiceStaff.star_level.desc(), ServiceStaff.id.asc())

            staff_result = await db.execute(staff_query)
            staff_list = staff_result.scalars().all()

            # 转换为VO对象
            available_staff = []
            for staff in staff_list:
                staff_vo = AvailableStaffVO(
                    id=staff.id,
                    uuid=staff.uuid,
                    real_name=staff.real_name or staff.user_name,
                    user_name=staff.user_name,
                    star_level=str(staff.star_level or 0),
                    service_cnt=staff.service_cnt or 0,
                    avatar=staff.avatar,
                    status=staff.status
                )
                available_staff.append(staff_vo.dict())

            logger.info(f"获取员工列表成功: store_uuid={store_uuid}, 数量={len(available_staff)}")
            return {"list": available_staff}

        except Exception as e:
            logger.error(f"获取员工列表失败: {str(e)}")
            return {"list": []}

    @staticmethod
    async def select_service_staff(
        db: AsyncSession,
        staff_request,
        current_user
    ) -> dict:
        """
        选择服务员工并复制到目标门店
        """
        try:

            # 1. 获取需求信息
            demand_query = select(DemandSquare).where(DemandSquare.uuid == staff_request.demand_uuid)
            demand_result = await db.execute(demand_query)
            demand = demand_result.scalar_one_or_none()

            if not demand:
                return {"success": False, "message": "需求信息不存在"}

            if not demand.source_order_id:
                return {"success": False, "message": "非共享订单，无需选择员工"}

            # 2. 获取原始订单信息
            order_query = select(
                Order.id,
                Order.order_number,
                Order.store_id,
                Order.store_uuid,
                Order.product_id,
                Order.product_name
            ).where(Order.id == demand.source_order_id)
            order_result = await db.execute(order_query)
            original_order = order_result.first()

            if not original_order:
                return {"success": False, "message": "原始订单不存在"}

            # 3. 获取目标门店信息（原始订单所属门店）
            target_store_query = select(Store).where(Store.id == original_order.store_id)
            target_store_result = await db.execute(target_store_query)
            target_store = target_store_result.scalar_one_or_none()

            if not target_store:
                return {"success": False, "message": "目标门店信息不存在"}

            # 4. 获取选中的员工信息
            selected_staff_query = select(ServiceStaff).where(ServiceStaff.id == staff_request.selected_staff_id)
            selected_staff_result = await db.execute(selected_staff_query)
            selected_staff = selected_staff_result.scalar_one_or_none()

            if not selected_staff:
                return {"success": False, "message": "选中的员工不存在"}

            # 验证员工归属权（只能选择当前用户门店的员工）
            user_store_uuid = current_user.user.store_uuid
            if selected_staff.store_uuid != user_store_uuid:
                return {"success": False, "message": "只能选择本门店的员工"}

            # === 开始事务操作 ===
            logger.info(f"开始员工复制操作: demand_uuid={staff_request.demand_uuid}, staff_id={staff_request.selected_staff_id}")

            # 5. 复制员工到目标门店（完整复制除公司绑定外的所有信息）
            new_staff_uuid = str(uuid.uuid4())
            copied_staff = ServiceStaff(
                uuid=new_staff_uuid,
                user_id=selected_staff.user_id,
                # 公司绑定相关字段 - 使用目标门店信息
                company_id=target_store.company_id,
                store_id=str(target_store.id),  # 转换为字符串
                store_uuid=target_store.store_uuid,
                store_name=target_store.name,

                # 完整复制原员工的所有其他信息
                job_type=selected_staff.job_type,
                user_name=selected_staff.user_name,
                nick_name=selected_staff.nick_name,
                real_name=selected_staff.real_name,
                mobile=selected_staff.mobile,
                password=selected_staff.password,

                # 微信相关信息
                wx_openid=selected_staff.wx_openid,
                wx_unionid=selected_staff.wx_unionid,
                wx_bind_time=selected_staff.wx_bind_time,
                wx_official_openid=selected_staff.wx_official_openid,
                wx_official_unionid=selected_staff.wx_official_unionid,
                wx_official_nickname=selected_staff.wx_official_nickname,
                wx_official_avatar=selected_staff.wx_official_avatar,
                wx_official_bind_time=selected_staff.wx_official_bind_time,
                is_bind_wx=selected_staff.is_bind_wx,

                # 员工属性信息
                star_level=selected_staff.star_level,
                status=selected_staff.status,
                work_type=selected_staff.work_type,
                is_delete=selected_staff.is_delete,
                is_part_time_job=selected_staff.is_part_time_job,
                is_old=selected_staff.is_old,
                is_allow_rob=selected_staff.is_allow_rob,

                # 个人信息
                sex=selected_staff.sex,
                age=selected_staff.age,
                avatar=selected_staff.avatar,
                city_id=selected_staff.city_id,
                city=selected_staff.city,
                address=selected_staff.address,

                # 服务统计信息
                service_cnt=selected_staff.service_cnt,
                service_uv=selected_staff.service_uv,
                service_commission=selected_staff.service_commission,
                sale_commission=selected_staff.sale_commission,

                # 归属信息
                own_user_id=selected_staff.own_user_id,
                own_user_name=selected_staff.own_user_name,

                # 保险信息
                insurance_start_time=selected_staff.insurance_start_time,
                insurance_end_time=selected_staff.insurance_end_time,

                # 邀请码
                invitation_code=selected_staff.invitation_code,

                # 创建信息
                created_by='system_copy',
                created_by_name='系统复制',
                create_time=datetime.now(),
                update_time=datetime.now()
            )

            db.add(copied_staff)
            logger.info("员工对象已添加到会话，准备flush获取ID")
            await db.flush()  # 获取新员工的ID
            logger.info(f"员工复制完成，新员工ID: {copied_staff.id}")

            # 6.1. 复制员工扩展信息
            await DemandSquareService._copy_staff_ext_info(db, selected_staff.id, copied_staff.id)

            # 6. 为复制的员工分配产品权限（直接使用原始订单的产品ID）
            if original_order.product_id:
                service_product = ServiceProduct(
                    staff_id=copied_staff.id,
                    productid=original_order.product_id,
                    store_uuid=target_store.store_uuid,
                    company_uuid=target_store.company_id,
                    create_time=datetime.now(),
                    update_time=datetime.now()
                )
                db.add(service_product)
                logger.info(f"为复制员工分配产品权限: staff_id={copied_staff.id}, product_id={original_order.product_id}")

            # 7. 将原始订单分配给复制的员工（通过order_waiter表）
            logger.info(f"开始订单分配: order_number={original_order.order_number}")

            # 首先删除可能存在的旧分配记录
            delete_old_assignment = text("""
                DELETE FROM order_waiter
                WHERE order_number = :order_number
            """)
            await db.execute(delete_old_assignment, {"order_number": original_order.order_number})
            logger.info("旧的订单分配记录已删除")

            # 插入新的员工分配记录
            insert_assignment = text("""
                INSERT INTO order_waiter (
                    order_number,
                    service_id,
                    service_name,
                    service_personal_commission,
                    service_personal,
                    create_time
                ) VALUES (
                    :order_number,
                    :service_id,
                    :service_name,
                    :service_personal_commission,
                    :service_personal,
                    NOW()
                )
            """)
            await db.execute(insert_assignment, {
                "order_number": original_order.order_number,
                "service_id": str(copied_staff.id),
                "service_name": copied_staff.real_name,
                "service_personal_commission": 0,  # 固定为0
                "service_personal": f"{float(demand.commission_amount):.2f}"  # 佣金金额，保留2位小数
            })
            logger.info(f"新的订单分配记录已插入: staff_id={copied_staff.id}")

            # 8. 更新原订单状态为已派单
            logger.info("开始更新原订单状态")
            update_order_query = update(Order).where(
                Order.id == original_order.id
            ).values(
                order_status=40,
                order_status_name="已派单",
                update_time=datetime.now()
            )
            await db.execute(update_order_query)
            logger.info(f"原订单状态已更新为已派单: order_id={original_order.id}")

            # 9. 更新demand_square记录，标记为已完成
            logger.info("开始更新需求广场状态")
            update_demand_query = update(DemandSquare).where(
                DemandSquare.uuid == staff_request.demand_uuid
            ).values(
                created_order_number=original_order.order_number,
                update_time=datetime.now()
            )
            await db.execute(update_demand_query)
            logger.info("需求广场状态更新完成")

            # === 准备响应数据（在提交前） ===
            response_data = {
                "success": True,
                "message": "员工选择成功，订单已分配",
                "order_number": original_order.order_number,
                "order_status": 20
            }

            # 准备日志信息（在提交前）
            success_log = f"员工选择成功: demand_uuid={staff_request.demand_uuid}, staff_id={staff_request.selected_staff_id}, new_staff_id={copied_staff.id}"

            # === 提交事务 ===
            logger.info("准备提交事务")
            await db.commit()
            logger.info("事务提交成功")

            # 记录成功日志（在事务提交后）
            logger.info(success_log)
            return response_data

        except Exception as e:
            # === 回滚事务 ===
            try:
                await db.rollback()
                logger.info("事务已回滚")
            except Exception as rollback_error:
                logger.error(f"事务回滚失败: {str(rollback_error)}")

            error_message = f"员工选择失败: {str(e)}"
            logger.error(error_message)

            return {
                "success": False,
                "message": error_message
            }

    @staticmethod
    async def cancel_demand_grab(
        db: AsyncSession,
        demand_uuid: str,
        current_user
    ) -> dict:
        """
        取消抢单，回滚需求状态
        """
        try:
            # 1. 获取需求信息
            demand_query = select(DemandSquare).where(DemandSquare.uuid == demand_uuid)
            demand_result = await db.execute(demand_query)
            demand = demand_result.scalar_one_or_none()

            if not demand:
                return {"success": False, "message": "需求不存在"}

            # 2. 检查是否为当前用户抢的单
            user_store_uuid = current_user.user.store_uuid
            if demand.grab_store_uuid != user_store_uuid:
                return {"success": False, "message": "只能取消自己抢的单"}

            # 3. 检查需求状态
            if demand.demand_status != 2:
                return {"success": False, "message": "该需求不是已抢单状态，无法取消"}

            # 4. 检查是否已经分配了员工（如果已分配员工，不允许取消）
            if demand.source_order_id:
                # 共享订单：检查是否已经有员工分配
                order_waiter_query = text("""
                    SELECT COUNT(*) as count
                    FROM order_waiter ow
                    LEFT JOIN `order` o ON ow.order_number = o.order_number
                    WHERE o.id = :source_order_id
                      AND ow.create_time > :grab_time
                """)
                result = await db.execute(order_waiter_query, {
                    "source_order_id": demand.source_order_id,
                    "grab_time": demand.grab_time
                })
                count = result.scalar()

                if count > 0:
                    return {"success": False, "message": "该订单已分配员工，无法取消抢单"}

            # 5. 回滚需求状态
            update_demand_query = update(DemandSquare).where(
                DemandSquare.uuid == demand_uuid
            ).values(
                demand_status=1,  # 回滚为待抢单
                grab_store_uuid=None,
                grab_store_name=None,
                grab_user_uuid=None,
                grab_user_name=None,
                grab_time=None,
                update_time=datetime.now()
            )

            await db.execute(update_demand_query)
            await db.commit()

            logger.info(f"取消抢单成功: demand_uuid={demand_uuid}, user_store={user_store_uuid}")
            return {
                "success": True,
                "message": "取消抢单成功",
                "demand_uuid": demand_uuid
            }

        except Exception as e:
            await db.rollback()
            logger.error(f"取消抢单失败: {str(e)}")
            return {"success": False, "message": f"取消抢单失败: {str(e)}"}

    @staticmethod
    async def adjust_shared_order_commission(
        db: AsyncSession,
        order_id: int,
        new_commission_amount: float,
        current_user
    ) -> dict:
        """
        调整已共享订单的佣金金额
        """
        try:
            from module_admin.entity.do.order import Order

            logger.info(f"开始调整佣金: order_id={order_id}, new_commission={new_commission_amount}, user_store={current_user.user.store_uuid}")

            # 1. 验证订单是否存在且属于当前用户门店
            try:
                order_query = select(
                    Order.id,
                    Order.order_number,
                    Order.store_uuid,
                    Order.pay_actual
                ).where(Order.id == order_id)
                order_result = await db.execute(order_query)
                order = order_result.first()

                if not order:
                    logger.warning(f"订单不存在: order_id={order_id}")
                    return {"success": False, "message": "订单不存在"}

                logger.info(f"订单查询成功: order_number={order.order_number}, store_uuid={order.store_uuid}")

            except Exception as e:
                logger.error(f"查询订单信息失败: order_id={order_id}, error={str(e)}")
                return {"success": False, "message": f"查询订单信息失败: {str(e)}"}

            # 验证订单归属权
            user_store_uuid = current_user.user.store_uuid
            if order.store_uuid != user_store_uuid:
                logger.warning(f"订单归属权验证失败: order_store={order.store_uuid}, user_store={user_store_uuid}")
                return {"success": False, "message": "只能调整本门店的订单佣金"}

            # 2. 查询订单的共享记录
            try:
                demand_query = select(DemandSquare).where(
                    and_(
                        DemandSquare.source_order_id == order_id,
                        DemandSquare.demand_status == 1  # 只有待抢单状态才能调整佣金
                    )
                )
                demand_result = await db.execute(demand_query)
                demand = demand_result.scalar_one_or_none()

                if not demand:
                    logger.warning(f"订单未共享或已被抢单: order_id={order_id}")
                    return {"success": False, "message": "订单未共享或已被抢单，无法调整佣金"}

                logger.info(f"共享记录查询成功: demand_uuid={demand.uuid}, current_commission={demand.commission_amount}")

            except Exception as e:
                logger.error(f"查询共享记录失败: order_id={order_id}, error={str(e)}")
                return {"success": False, "message": f"查询共享记录失败: {str(e)}"}

            # 3. 验证新佣金金额
            if new_commission_amount <= 0:
                return {"success": False, "message": "佣金金额必须大于0"}

            if new_commission_amount > float(order.pay_actual or 0):
                return {"success": False, "message": "佣金金额不能超过订单总金额"}

            # 4. 验证只能增加佣金，不能减少
            current_commission = float(demand.commission_amount or 0)
            if new_commission_amount <= current_commission:
                return {"success": False, "message": f"新佣金金额必须大于当前佣金金额¥{current_commission}"}

            # 5. 更新佣金金额
            try:
                old_commission = demand.commission_amount
                logger.info(f"准备更新佣金: demand_id={demand.id}, old_commission={old_commission}, new_commission={new_commission_amount}")

                update_query = update(DemandSquare).where(
                    DemandSquare.id == demand.id
                ).values(
                    commission_amount=new_commission_amount,
                    update_time=datetime.now()
                )

                result = await db.execute(update_query)
                logger.info(f"数据库更新执行完成: affected_rows={result.rowcount}")

                await db.commit()
                logger.info(f"事务提交成功")

                logger.info(f"佣金调整成功: order_id={order_id}, order_number={order.order_number}, old_commission={old_commission}, new_commission={new_commission_amount}")

                return {
                    "success": True,
                    "message": "佣金调整成功",
                    "old_commission": float(old_commission),
                    "new_commission": new_commission_amount,
                    "order_id": order_id,
                    "demand_uuid": demand.uuid
                }

            except Exception as e:
                await db.rollback()
                logger.error(f"更新佣金失败: demand_id={demand.id}, error={str(e)}")
                return {"success": False, "message": f"更新佣金失败: {str(e)}"}

        except Exception as e:
            try:
                await db.rollback()
                logger.info("事务回滚成功")
            except Exception as rollback_error:
                logger.error(f"事务回滚失败: {str(rollback_error)}")

            logger.error(f"调整佣金失败: order_id={order_id}, user_id={current_user.user.id}, error={str(e)}", exc_info=True)

            # 根据错误类型返回更具体的错误信息
            if "Unknown column" in str(e):
                return {"success": False, "message": "数据库字段错误，请联系技术支持"}
            elif "doesn't exist" in str(e):
                return {"success": False, "message": "数据表不存在，请联系技术支持"}
            elif "Connection" in str(e):
                return {"success": False, "message": "数据库连接异常，请稍后重试"}
            else:
                return {"success": False, "message": f"调整佣金失败: {str(e)}"}

    @staticmethod
    async def check_order_shared_status(
        db: AsyncSession,
        order_id: int
    ) -> dict:
        """
        检查订单是否已共享到家政人广场
        """
        try:
            # 查询订单是否在需求广场中
            demand_query = select(DemandSquare).where(
                and_(
                    DemandSquare.source_order_id == order_id,
                    DemandSquare.demand_status.in_([1, 2])  # 待抢单或已抢单
                )
            )
            demand_result = await db.execute(demand_query)
            demand = demand_result.scalar_one_or_none()

            if demand:
                return {
                    "success": True,
                    "is_shared": True,
                    "demand_uuid": demand.uuid,
                    "demand_status": demand.demand_status,
                    "demand_status_name": "待抢单" if demand.demand_status == 1 else "已抢单",
                    "commission_amount": float(demand.commission_amount or 0),
                    "grab_store_name": demand.grab_store_name,
                    "grab_time": demand.grab_time.isoformat() if demand.grab_time else None
                }
            else:
                return {
                    "success": True,
                    "is_shared": False
                }

        except Exception as e:
            logger.error(f"检查订单共享状态失败: {str(e)}")
            return {"success": False, "message": f"检查订单共享状态失败: {str(e)}"}

    @staticmethod
    async def expire_shared_order(
        db: AsyncSession,
        order_id: int,
        current_user
    ) -> dict:
        """
        将共享订单设置为过期状态
        """
        try:
            # 1. 查询订单的共享记录
            demand_query = select(DemandSquare).where(
                and_(
                    DemandSquare.source_order_id == order_id,
                    DemandSquare.demand_status.in_([1, 2])  # 待抢单或已抢单
                )
            )
            demand_result = await db.execute(demand_query)
            demand = demand_result.scalar_one_or_none()

            if not demand:
                return {"success": False, "message": "订单未共享或已处理"}

            # 2. 检查是否为订单所属门店的用户
            order_query = select(Order).where(Order.id == order_id)
            order_result = await db.execute(order_query)
            order = order_result.scalar_one_or_none()

            if not order:
                return {"success": False, "message": "订单不存在"}

            user_store_uuid = current_user.user.store_uuid
            if order.store_uuid != user_store_uuid:
                return {"success": False, "message": "只能操作本门店的订单"}

            # 3. 如果已被抢单，需要先回滚抢单状态
            if demand.demand_status == 2:
                # 检查是否已分配员工
                order_waiter_query = text("""
                    SELECT COUNT(*) as count
                    FROM order_waiter ow
                    WHERE ow.order_number = :order_number
                      AND ow.create_time > :grab_time
                """)
                result = await db.execute(order_waiter_query, {
                    "order_number": order.order_number,
                    "grab_time": demand.grab_time
                })
                count = result.scalar()

                if count > 0:
                    return {"success": False, "message": "订单已被接单并分配员工，无法取消共享"}

            # 4. 更新需求状态为过期
            update_demand_query = update(DemandSquare).where(
                DemandSquare.uuid == demand.uuid
            ).values(
                demand_status=3,  # 设置为过期状态
                update_time=datetime.now()
            )

            await db.execute(update_demand_query)
            await db.commit()

            logger.info(f"共享订单已设置为过期: order_id={order_id}, demand_uuid={demand.uuid}")
            return {
                "success": True,
                "message": "共享订单已取消，可以进行派单",
                "demand_uuid": demand.uuid
            }

        except Exception as e:
            await db.rollback()
            logger.error(f"设置共享订单过期失败: {str(e)}")
            return {"success": False, "message": f"操作失败: {str(e)}"}

    @staticmethod
    async def _copy_staff_ext_info(
        db: AsyncSession,
        original_staff_id: int,
        new_staff_id: int
    ):
        """
        复制员工扩展信息
        """
        try:
            # 查询原始员工的扩展信息
            original_ext_query = text("""
                SELECT
                    id_number, birthday, lng, lat, live_pos, native_place, nation,
                    marriage_status, education_background, height, weight, family_address,
                    source, province_id, province_name, area_id, area_name, address_desc,
                    emergency_contact_name, emergency_contact_relation, emergency_contact_phone,
                    emergency_contact_address, start_time, end_time, rest_days
                FROM service_staff_ext
                WHERE staff_id = :staff_id
                LIMIT 1
            """)

            original_ext_result = await db.execute(original_ext_query, {"staff_id": original_staff_id})
            original_ext = original_ext_result.fetchone()

            if original_ext:
                # 创建新的员工扩展信息
                insert_ext_sql = text("""
                    INSERT INTO service_staff_ext (
                        staff_id, id_number, birthday, lng, lat, live_pos, native_place, nation,
                        marriage_status, education_background, height, weight, family_address,
                        source, province_id, province_name, area_id, area_name, address_desc,
                        emergency_contact_name, emergency_contact_relation, emergency_contact_phone,
                        emergency_contact_address, start_time, end_time, rest_days,
                        created_by, created_at, updated_at
                    ) VALUES (
                        :staff_id, :id_number, :birthday, :lng, :lat, :live_pos, :native_place, :nation,
                        :marriage_status, :education_background, :height, :weight, :family_address,
                        :source, :province_id, :province_name, :area_id, :area_name, :address_desc,
                        :emergency_contact_name, :emergency_contact_relation, :emergency_contact_phone,
                        :emergency_contact_address, :start_time, :end_time, :rest_days,
                        'system_copy', NOW(), NOW()
                    )
                """)

                await db.execute(insert_ext_sql, {
                    "staff_id": new_staff_id,
                    "id_number": original_ext.id_number,
                    "birthday": original_ext.birthday,
                    "lng": original_ext.lng,
                    "lat": original_ext.lat,
                    "live_pos": original_ext.live_pos,
                    "native_place": original_ext.native_place,
                    "nation": original_ext.nation,
                    "marriage_status": original_ext.marriage_status,
                    "education_background": original_ext.education_background,
                    "height": original_ext.height,
                    "weight": original_ext.weight,
                    "family_address": original_ext.family_address,
                    "source": original_ext.source,
                    "province_id": original_ext.province_id,
                    "province_name": original_ext.province_name,
                    "area_id": original_ext.area_id,
                    "area_name": original_ext.area_name,
                    "address_desc": original_ext.address_desc,
                    "emergency_contact_name": original_ext.emergency_contact_name,
                    "emergency_contact_relation": original_ext.emergency_contact_relation,
                    "emergency_contact_phone": original_ext.emergency_contact_phone,
                    "emergency_contact_address": original_ext.emergency_contact_address,
                    "start_time": original_ext.start_time,
                    "end_time": original_ext.end_time,
                    "rest_days": original_ext.rest_days
                })

                logger.info(f"员工扩展信息复制成功: original_staff_id={original_staff_id}, new_staff_id={new_staff_id}")
            else:
                # 如果原始员工没有扩展信息，创建默认的扩展信息
                insert_default_ext_sql = text("""
                    INSERT INTO service_staff_ext (
                        staff_id, marriage_status, education_background, height, weight,
                        emergency_contact_relation, area_id, start_time, end_time, rest_days,
                        created_by, created_at, updated_at
                    ) VALUES (
                        :staff_id, '0', '0', '0', '0', '0', '0', 8, 18, '',
                        'system_copy', NOW(), NOW()
                    )
                """)

                await db.execute(insert_default_ext_sql, {"staff_id": new_staff_id})
                logger.info(f"创建默认员工扩展信息: new_staff_id={new_staff_id}")

        except Exception as e:
            logger.error(f"复制员工扩展信息失败: {str(e)}")
            # 不抛出异常，避免影响主流程
            pass
