<template>
  <view class="container">
    <!-- 顶部背景区域 -->
    <view class="header-background-section">
      <view class="header-background"></view>
      <view class="header-content">
        <!-- 顶部导航栏 -->
        <view class="header-section">
          <view class="navbar">
            <view class="nav-back" @tap="goBack">
              <u-icon name="arrow-left" color="#fff" size="20"></u-icon>
            </view>
            <text class="nav-title">共享订单设置</text>
            <view class="nav-placeholder"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 订单信息卡片 -->
    <view class="order-info-card">
      <view class="card-header">
        <view class="header-icon">
          <u-icon name="file-text" size="20" color="#fff"></u-icon>
        </view>
        <text class="card-title">订单信息</text>
      </view>
      <view class="order-details">
        <view class="detail-row">
          <text class="detail-label">订单号：</text>
          <text class="detail-value">{{ orderInfo.order_number || 'N/A' }}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">服务项目：</text>
          <text class="detail-value">{{ orderInfo.product_name || 'N/A' }}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">订单金额：</text>
          <text class="detail-value amount">¥{{ orderInfo.total_amount || '0.00' }}</text>
        </view>
      </view>
    </view>

    <!-- 佣金设置卡片 -->
    <view class="setting-card">
      <view class="card-header">
        <view class="header-icon commission-icon">
          <u-icon name="gift" size="20" color="#fff"></u-icon>
        </view>
        <text class="card-title">佣金设置</text>
      </view>
      <view class="setting-content">
        <view class="input-section">
          <text class="input-label">佣金金额</text>
          <view class="input-container">
            <text class="currency-symbol">¥</text>
            <input
              type="digit"
              v-model="commissionAmount"
              placeholder="0.00"
              class="amount-input"
              @input="validateCommissionInput"
            />
          </view>
          <text class="input-tip">佣金金额不能超过订单总金额</text>
        </view>
      </view>
    </view>

    <!-- 过期时间设置卡片 -->
    <view class="setting-card">
      <view class="card-header">
        <view class="header-icon time-icon">
          <u-icon name="clock" size="20" color="#fff"></u-icon>
        </view>
        <text class="card-title">过期时间设置</text>
      </view>
      <view class="setting-content">
        <view class="time-options">
          <view 
            class="time-option" 
            v-for="option in timeOptions" 
            :key="option.value"
            :class="{ active: selectedExpireHours === option.value }"
            @click="selectExpireTime(option.value)"
          >
            <view class="option-content">
              <text class="option-text">{{ option.label }}</text>
              <view class="option-radio" :class="{ checked: selectedExpireHours === option.value }">
                <view class="radio-dot" v-if="selectedExpireHours === option.value"></view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 自定义时间输入 -->
        <view class="custom-time-section" v-if="selectedExpireHours === 'custom'">
          <view class="custom-input-row">
            <input
              type="number"
              v-model="customHours"
              placeholder="请输入小时数"
              class="custom-time-input"
              @input="validateCustomTime"
            />
            <text class="time-unit">小时</text>
          </view>
          <text class="custom-tip">自定义时间范围：1-168小时（7天）</text>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="action-area">
        <view class="cancel-btn" @tap="goBack">
          <text>取消</text>
        </view>
        <view class="confirm-btn primary-btn" @tap="confirmShare" :class="{ disabled: !canConfirm }">
          <text>确认共享</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      orderInfo: {},
      commissionAmount: '',
      selectedExpireHours: 48, // 默认48小时
      customHours: '',
      timeOptions: [
        { label: '1小时', value: 1 },
        { label: '2小时', value: 2 },
        { label: '6小时', value: 6 },
        { label: '12小时', value: 12 },
        { label: '24小时', value: 24 },
        { label: '48小时', value: 48 },
        { label: '72小时', value: 72 },
        { label: '自定义', value: 'custom' }
      ]
    };
  },
  computed: {
    // 获取最终的过期小时数
    finalExpireHours() {
      if (this.selectedExpireHours === 'custom') {
        return parseInt(this.customHours) || 1;
      }
      return this.selectedExpireHours;
    },
    // 是否可以确认
    canConfirm() {
      const hasValidCommission = this.commissionAmount && parseFloat(this.commissionAmount) > 0;
      const hasValidExpireTime = this.selectedExpireHours !== 'custom' || 
        (this.customHours && parseInt(this.customHours) >= 1 && parseInt(this.customHours) <= 168);
      return hasValidCommission && hasValidExpireTime;
    }
  },
  onLoad(options) {
    console.log('共享订单设置页面参数:', options);
    
    if (options.orderData) {
      try {
        this.orderInfo = JSON.parse(decodeURIComponent(options.orderData));
        console.log('订单信息:', this.orderInfo);
      } catch (error) {
        console.error('解析订单数据失败:', error);
        uni.showToast({
          title: '订单数据错误',
          icon: 'none'
        });
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }
    } else {
      uni.showToast({
        title: '缺少订单信息',
        icon: 'none'
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 验证佣金输入
    validateCommissionInput(e) {
      let value = e.detail.value;
      // 只允许数字和一个小数点
      value = value.replace(/[^\d.]/g, '');
      // 只允许一个小数点
      const parts = value.split('.');
      if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('');
      }
      // 限制小数点后两位
      if (parts[1] && parts[1].length > 2) {
        value = parts[0] + '.' + parts[1].substring(0, 2);
      }
      this.commissionAmount = value;
    },
    
    // 选择过期时间
    selectExpireTime(value) {
      this.selectedExpireHours = value;
      if (value !== 'custom') {
        this.customHours = '';
      }
    },
    
    // 验证自定义时间
    validateCustomTime(e) {
      let value = e.detail.value;
      // 只允许数字
      value = value.replace(/[^\d]/g, '');
      // 限制范围 1-168
      if (value && (parseInt(value) < 1 || parseInt(value) > 168)) {
        if (parseInt(value) < 1) value = '1';
        if (parseInt(value) > 168) value = '168';
      }
      this.customHours = value;
    },
    
    // 确认共享
    async confirmShare() {
      if (!this.canConfirm) {
        uni.showToast({
          title: '请完善设置信息',
          icon: 'none'
        });
        return;
      }
      
      const commission = parseFloat(this.commissionAmount);
      
      // 验证佣金金额
      if (isNaN(commission) || commission <= 0) {
        uni.showToast({
          title: '请输入有效的佣金金额',
          icon: 'none'
        });
        return;
      }
      
      if (commission > parseFloat(this.orderInfo.total_amount || 0)) {
        uni.showToast({
          title: '佣金金额不能超过订单金额',
          icon: 'none'
        });
        return;
      }
      
      // 最终确认弹窗
      const expireText = this.selectedExpireHours === 'custom' 
        ? `${this.customHours}小时` 
        : this.timeOptions.find(opt => opt.value === this.selectedExpireHours)?.label || '';
        
      uni.showModal({
        title: '确认共享',
        content: `确定要将订单共享到家政广场吗？\n\n订单号：${this.orderInfo.order_number}\n佣金金额：${commission}元\n过期时间：${expireText}\n\n共享后其他门店可以接单并获得佣金。`,
        success: async (res) => {
          if (res.confirm) {
            await this.executeShare(commission, this.finalExpireHours);
          }
        }
      });
    },
    
    // 执行共享
    async executeShare(commissionAmount, expireHours) {
      try {
        uni.showLoading({
          title: '正在共享...'
        });
        
        // 调用共享订单API
        const response = await this.$post('/api/v1/demand-square/share-order', {
          order_id: this.orderInfo.id,
          order_number: this.orderInfo.order_number,
          commission_amount: commissionAmount,
          expire_hours: expireHours
        });
        
        uni.hideLoading();
        
        if (response && response.success !== false) {
          uni.showToast({
            title: '订单已共享到家政广场',
            icon: 'success',
            duration: 2000
          });
          
          // 延迟返回上一页
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          uni.showToast({
            title: response.msg || '共享失败，请重试',
            icon: 'none',
            duration: 2000
          });
        }
      } catch (error) {
        console.error('共享订单失败:', error);
        uni.hideLoading();
        
        uni.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

// 顶部背景区域
.header-background-section {
  position: relative;
  padding-bottom: 40rpx;
  margin-bottom: 30rpx;

  .header-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
    border-radius: 0 0 40rpx 40rpx;
    z-index: 1;
  }

  .header-content {
    position: relative;
    z-index: 2;
    padding: 20rpx 30rpx 0;
  }
}

// 顶部导航区域
.header-section {
  padding-top: var(--status-bar-height);
}

.navbar {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  position: relative;
}

.nav-back,
.nav-placeholder {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;

  &:active {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0.95);
  }
}

.nav-placeholder {
  background: transparent;
  border: none;
}

.nav-title {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
  pointer-events: none;
}

// 卡片通用样式
.order-info-card,
.setting-card {
  margin: 0 30rpx 30rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1rpx solid #f0f0f0;
}

.header-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.commission-icon {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.time-icon {
  background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

// 订单信息样式
.order-details {
  padding: 30rpx;
}

.detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.detail-label {
  font-size: 28rpx;
  color: #666;
  min-width: 160rpx;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;

  &.amount {
    color: #ff6b35;
    font-weight: 600;
    font-size: 32rpx;
  }
}

// 设置内容样式
.setting-content {
  padding: 30rpx;
}

.input-section {
  margin-bottom: 20rpx;
}

.input-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.input-container {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 20rpx 24rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;

  &:focus-within {
    border-color: #fdd118;
    background: #fff;
  }
}

.currency-symbol {
  font-size: 28rpx;
  color: #666;
  margin-right: 8rpx;
}

.amount-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  border: none;
  background: transparent;
  outline: none;
}

.input-tip {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-top: 12rpx;
}

// 时间选项样式
.time-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.time-option {
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;

  &.active {
    border-color: #fdd118;
    background: #fff9e6;
  }

  &:active {
    transform: scale(0.98);
  }
}

.option-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
}

.option-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.option-radio {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &.checked {
    border-color: #fdd118;
    background: #fdd118;
  }
}

.radio-dot {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background: #fff;
}

// 自定义时间样式
.custom-time-section {
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #f0f0f0;
}

.custom-input-row {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 20rpx 24rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;

  &:focus-within {
    border-color: #fdd118;
    background: #fff;
  }
}

.custom-time-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  border: none;
  background: transparent;
  outline: none;
}

.time-unit {
  font-size: 28rpx;
  color: #666;
  margin-left: 8rpx;
}

.custom-tip {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-top: 12rpx;
}

// 底部操作栏
.bottom-bar {
  margin-top: auto;
  padding: 30rpx;
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.action-area {
  display: flex;
  gap: 20rpx;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-weight: 600;
}

.cancel-btn {
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  color: #666;

  &:active {
    background: #e9ecef;
    transform: scale(0.98);
  }
}

.confirm-btn {
  background: linear-gradient(135deg, #fdd118, #ff801c);
  border: 2rpx solid #fdd118;
  color: #fff;

  &:active {
    background: linear-gradient(135deg, #e6c015, #e6721a);
    transform: scale(0.98);
  }

  &.disabled {
    background: #f0f0f0;
    border-color: #f0f0f0;
    color: #ccc;

    &:active {
      transform: none;
      background: #f0f0f0;
    }
  }
}
</style>
